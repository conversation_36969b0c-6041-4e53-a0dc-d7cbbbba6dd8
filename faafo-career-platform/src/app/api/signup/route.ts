import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from 'uuid';
import React from 'react';
import prisma from '../../../lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return NextResponse.json({ message: "User already exists" }, { status: 409 });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the new user (unverified)
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        emailVerified: null, // Explicitly set as unverified
      },
    });

    // Generate verification token
    const verificationToken = uuidv4();
    const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification token
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: tokenExpiry,
      },
    });

    // Send verification email
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;

    try {
      await sendEmail({
        to: email,
        subject: "Verify your email for FAAFO Career Platform",
        template: React.createElement(VerificationEmail, { username: email, verificationLink: verificationUrl }),
      });
      console.log(`Verification email sent to ${email}`);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail the registration if email fails, but log it
    }

    return NextResponse.json({
      message: "User created successfully. Please check your email to verify your account.",
      requiresVerification: true
    }, { status: 201 });

  } catch (error) {
    console.error("Error during signup:", error);
    return NextResponse.json({ message: "Something went wrong" }, { status: 500 });
  }
}