import { z } from 'zod';

// User validation schemas
export const signupSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export const emailSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
});

// Profile validation schemas
export const profileUpdateSchema = z.object({
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  profilePictureUrl: z.string().url('Invalid URL').optional().or(z.literal('')),
  socialMediaLinks: z.record(z.string().url('Invalid URL')).optional(),

  // Personal Information
  firstName: z.string().max(50, 'First name must be less than 50 characters').optional(),
  lastName: z.string().max(50, 'Last name must be less than 50 characters').optional(),
  phoneNumber: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format').optional().or(z.literal('')),
  location: z.string().max(100, 'Location must be less than 100 characters').optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),

  // Professional Information
  jobTitle: z.string().max(100, 'Job title must be less than 100 characters').optional(),
  company: z.string().max(100, 'Company name must be less than 100 characters').optional(),
  currentIndustry: z.string().max(100, 'Industry must be less than 100 characters').optional(),
  targetIndustry: z.string().max(100, 'Target industry must be less than 100 characters').optional(),
  experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),

  // Career Development
  careerInterests: z.array(z.string().max(50)).max(10, 'Maximum 10 career interests allowed').optional(),
  skillsToLearn: z.array(z.string().max(50)).max(20, 'Maximum 20 skills allowed').optional(),
  weeklyLearningGoal: z.number().min(1, 'Weekly goal must be at least 1 hour').max(168, 'Weekly goal cannot exceed 168 hours').optional(),

  // Privacy & Preferences
  profileVisibility: z.enum(['PRIVATE', 'PUBLIC', 'COMMUNITY_ONLY']).optional(),
  emailNotifications: z.boolean().optional(),
  profilePublic: z.boolean().optional(),
  showEmail: z.boolean().optional(),
  showPhone: z.boolean().optional(),
});

// Assessment validation schemas
export const assessmentResponseSchema = z.object({
  questionKey: z.string().min(1, 'Question key is required'),
  answerValue: z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.array(z.string()),
    z.null()
  ]),
});

export const assessmentSaveSchema = z.object({
  currentStep: z.number().min(0, 'Current step must be non-negative'),
  formData: z.record(z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.array(z.string()),
    z.null()
  ])),
  status: z.enum(['IN_PROGRESS', 'COMPLETED']).optional(),
});

// Freedom Fund validation schemas
export const freedomFundSchema = z.object({
  monthlyExpenses: z.number().positive('Monthly expenses must be positive'),
  coverageMonths: z.number().int().positive('Coverage months must be a positive integer'),
  targetSavings: z.number().positive('Target savings must be positive'),
  currentSavingsAmount: z.number().min(0, 'Current savings cannot be negative').optional(),
});

// Learning Resource validation schemas
export const learningResourceSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  url: z.string().url('Invalid URL'),
  type: z.enum(['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP']),
  category: z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP']),
  skillLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  author: z.string().max(100, 'Author name must be less than 100 characters').optional(),
  duration: z.string().max(50, 'Duration must be less than 50 characters').optional(),
  cost: z.enum(['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION']).default('FREE'),
  format: z.enum(['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL']),
});

// Resource Rating validation schemas
export const resourceRatingSchema = z.object({
  resourceId: z.string().uuid('Invalid resource ID'),
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  review: z.string().max(1000, 'Review must be less than 1000 characters').optional(),
  isHelpful: z.boolean().optional(),
});

// Learning Progress validation schemas
export const learningProgressSchema = z.object({
  resourceId: z.string().uuid('Invalid resource ID'),
  status: z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED']),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5').optional(),
  review: z.string().max(1000, 'Review must be less than 1000 characters').optional(),
});

// Forum validation schemas
export const forumPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  content: z.string().min(1, 'Content is required').max(5000, 'Content must be less than 5000 characters'),
});

export const forumReplySchema = z.object({
  content: z.string().min(1, 'Content is required').max(2000, 'Content must be less than 2000 characters'),
  postId: z.string().uuid('Invalid post ID'),
});

// Contact form validation schema
export const contactFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject must be less than 200 characters'),
  message: z.string().min(1, 'Message is required').max(2000, 'Message must be less than 2000 characters'),
});

// Career Path validation schemas
export const careerPathSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug must be less than 100 characters'),
  overview: z.string().min(1, 'Overview is required').max(2000, 'Overview must be less than 2000 characters'),
  pros: z.string().min(1, 'Pros are required'),
  cons: z.string().min(1, 'Cons are required'),
  actionableSteps: z.array(z.object({
    title: z.string().min(1, 'Step title is required'),
    description: z.string().min(1, 'Step description is required'),
  })),
  isActive: z.boolean().default(true),
});

// Pagination validation schema
export const paginationSchema = z.object({
  page: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 10),
});

// Resource filter validation schema
export const resourceFilterSchema = z.object({
  category: z.string().optional(),
  type: z.string().optional(),
  skillLevel: z.string().optional(),
  cost: z.string().optional(),
  format: z.string().optional(),
  search: z.string().optional(),
});

// Utility function to validate request body
export function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return { success: false, error: errorMessage };
    }
    return { success: false, error: 'Invalid data format' };
  }
}

// Utility function to validate input
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return { success: false, error: errorMessage };
    }
    return { success: false, error: 'Invalid data format' };
  }
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  message?: string;
}

export const rateLimitConfigs = {
  auth: { windowMs: 15 * 60 * 1000, maxRequests: 5, message: 'Too many authentication attempts' }, // 5 attempts per 15 minutes
  api: { windowMs: 15 * 60 * 1000, maxRequests: 100, message: 'Too many API requests' }, // 100 requests per 15 minutes
  contact: { windowMs: 60 * 60 * 1000, maxRequests: 3, message: 'Too many contact form submissions' }, // 3 submissions per hour
} as const;
